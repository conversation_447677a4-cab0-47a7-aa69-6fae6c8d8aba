#!/usr/bin/env python3
"""
Test script to validate the dual-mode conditional implementation.
Tests both direct mode and variable mode functionality.
"""

import os
import sys
import re


def validate_workflow_service_dual_mode():
    """Validate ConditionalNode dual-mode implementation in workflow service."""
    
    print("🔍 Validating ConditionalNode dual-mode implementation...")
    
    # Read the ConditionalNode file
    file_path = "workflow-service/app/components/control_flow/conditionalNode.py"
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Test 1: Check for dual-mode toggle
    if 'use_variable_for_conditions' in content:
        print("✅ Found 'use_variable_for_conditions' toggle")
    else:
        print("❌ 'use_variable_for_conditions' toggle not found")
        return False
    
    # Test 2: Check for conditional variable name input
    if 'condition_variable_name' in content:
        print("✅ Found 'condition_variable_name' input")
    else:
        print("❌ 'condition_variable_name' input not found")
        return False
    
    # Test 3: Check for visibility rules on variable name
    if 'visibility_rules' in content and 'use_variable_for_conditions' in content:
        print("✅ Found visibility rules for variable name field")
    else:
        print("❌ Visibility rules for variable name field not found")
        return False
    
    # Test 4: Check for dual-mode documentation
    if "DUAL-MODE DESIGN" in content:
        print("✅ Found dual-mode design documentation")
    else:
        print("❌ Dual-mode design documentation missing")
        return False
    
    # Test 5: Check for evaluation mode logic
    if 'if use_variable_for_conditions:' in content:
        print("✅ Found evaluation mode logic")
    else:
        print("❌ Evaluation mode logic not found")
        return False
    
    # Test 6: Check for global context access
    if 'context.global_context.get' in content:
        print("✅ Found global context access for variable mode")
    else:
        print("❌ Global context access not found")
        return False
    
    # Test 7: Check for data separation (evaluation vs routing)
    if 'evaluation_data' in content and 'input_data' in content:
        print("✅ Found data separation (evaluation_data vs input_data)")
    else:
        print("❌ Data separation not implemented")
        return False
    
    return True


def validate_node_executor_dual_mode():
    """Validate ConditionalComponent dual-mode implementation in node executor."""
    
    print("\n🔍 Validating ConditionalComponent dual-mode implementation...")
    
    # Read the ConditionalComponent file
    file_path = "node-executor-service/app/components/conditional_component.py"
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Test 1: Check for dual-mode schema fields
    if 'use_variable_for_conditions: bool' in content:
        print("✅ Found 'use_variable_for_conditions' in schema")
    else:
        print("❌ 'use_variable_for_conditions' not in schema")
        return False
    
    if 'condition_variable_name: Optional[str]' in content:
        print("✅ Found 'condition_variable_name' in schema")
    else:
        print("❌ 'condition_variable_name' not in schema")
        return False
    
    if 'global_context: Dict[str, Any]' in content:
        print("✅ Found 'global_context' in schema")
    else:
        print("❌ 'global_context' not in schema")
        return False
    
    # Test 2: Check for validation logic
    if 'validate_condition_variable_name' in content:
        print("✅ Found validation for condition variable name")
    else:
        print("❌ Validation for condition variable name not found")
        return False
    
    # Test 3: Check for dual-mode evaluation logic
    if 'if use_variable_for_conditions:' in content:
        print("✅ Found dual-mode evaluation logic")
    else:
        print("❌ Dual-mode evaluation logic not found")
        return False
    
    # Test 4: Check for global context access
    if 'global_context.get(condition_variable_name)' in content:
        print("✅ Found global context variable access")
    else:
        print("❌ Global context variable access not found")
        return False
    
    # Test 5: Check for metadata tracking
    if 'evaluation_mode' in content:
        print("✅ Found evaluation mode metadata tracking")
    else:
        print("❌ Evaluation mode metadata tracking not found")
        return False
    
    # Test 6: Check for updated method signatures
    if 'evaluation_data: Any' in content and 'input_data: Any' in content:
        print("✅ Found updated method signatures with data separation")
    else:
        print("❌ Updated method signatures not found")
        return False
    
    return True


def validate_syntax():
    """Validate Python syntax of both files."""
    
    print("\n🔍 Validating Python syntax...")
    
    # Check workflow service file
    workflow_file = "workflow-service/app/components/control_flow/conditionalNode.py"
    if os.path.exists(workflow_file):
        result = os.system(f"python3 -m py_compile {workflow_file}")
        if result == 0:
            print("✅ Workflow service ConditionalNode syntax valid")
        else:
            print("❌ Workflow service ConditionalNode syntax invalid")
            return False
    
    # Check node executor file
    executor_file = "node-executor-service/app/components/conditional_component.py"
    if os.path.exists(executor_file):
        result = os.system(f"python3 -m py_compile {executor_file}")
        if result == 0:
            print("✅ Node executor ConditionalComponent syntax valid")
        else:
            print("❌ Node executor ConditionalComponent syntax invalid")
            return False
    
    return True


def main():
    """Main validation function."""
    print("=" * 60)
    print("DUAL-MODE CONDITIONAL VALIDATION REPORT")
    print("=" * 60)
    
    # Change to backend directory
    os.chdir("/Users/<USER>/Desktop/ruh_ai/backend")
    
    # Run validations
    workflow_success = validate_workflow_service_dual_mode()
    executor_success = validate_node_executor_dual_mode()
    syntax_success = validate_syntax()
    
    overall_success = workflow_success and executor_success and syntax_success
    
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    
    if overall_success:
        print("✅ ALL VALIDATIONS PASSED")
        print()
        print("🎉 Dual-mode conditional implementation completed successfully!")
        print()
        print("Features implemented:")
        print("   ✓ Toggle between direct mode and variable mode")
        print("   ✓ Direct mode: conditions evaluate against input_data")
        print("   ✓ Variable mode: conditions evaluate against global variable")
        print("   ✓ Input data routing preserved in both modes")
        print("   ✓ Conditional variable name input with visibility rules")
        print("   ✓ Comprehensive validation and error handling")
        print("   ✓ Metadata tracking for evaluation mode")
        print("   ✓ Updated documentation for dual-mode design")
        print()
        print("Usage:")
        print("   • Toggle OFF (default): Direct mode - conditions check input_data")
        print("   • Toggle ON: Variable mode - conditions check global variable")
        print("   • Variable name field appears when toggle is ON")
        print("   • Input data always flows through for routing")
        
        sys.exit(0)
    else:
        print("❌ SOME VALIDATIONS FAILED")
        print("Please check the implementation and fix any issues.")
        sys.exit(1)


if __name__ == "__main__":
    main()