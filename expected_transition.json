{"tool_params": {"items": [{"field_name": "conditions", "data_type": "array", "field_value": [{"operator": "", "variable_name": "if global context", "expected_value": "if operator is not (is_empty, exists)", "next_transition": "transition-ConditionalNode-1752495520951"}]}, {"field_name": "input_data", "data_type": "string", "field_value": ""}, {"field_name": "source", "data_type": "string", "field_value": "global_context|node_output"}]}}