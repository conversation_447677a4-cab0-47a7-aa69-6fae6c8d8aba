{"tools_to_use": [{"tool_id": 1, "tool_name": "conditional", "server_id": "node-executor-service", "tool_params": {"items": [{"field_name": "conditions", "data_type": "array", "field_value": [{"source": "node_output", "operator": "is_empty", "next_transition": "transition-ConditionalNode-1752495520951"}, {"source": "global_context", "operator": "equals", "expected_value": "hello", "next_transition": "transition-ConditionalNode-1752495520951"}, {"source": "node_output", "operator": "equals", "expected_value": "wow", "next_transition": "transition-ConditionalNode-1752495520951"}]}, {"field_name": "default_transition", "data_type": "string", "field_value": "transition-MergeDataComponent-1752495362620"}, {"field_name": "evaluation_strategy", "data_type": "string", "field_value": "all_matches"}]}}]}