    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "4142c980-e899-4e06-ab9e-e72fe7f52b69",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:24 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "is_empty",
        "expected_value": "node_output",
        "next_transition": "transition-ConditionalNode-*************"
      }
    ],
    "default_transition": "transition-MergeDataComponent-1752495362620",
    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "4142c980-e899-4e06-ab9e-e72fe7f52b69",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:24 - ComponentSystem - INFO - [_process_message:713] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool conditional for RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:24 - ComponentSystem - INFO - [_process_message] Executing tool conditional for RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool:94] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool:97] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "is_empty",
        "expected_value": "node_output",
        "next_transition": "transition-ConditionalNode-*************"
      }
    ],
    "default_transition": "transition-MergeDataComponent-1752495362620",
    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "4142c980-e899-4e06-ab9e-e72fe7f52b69",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "is_empty",
        "expected_value": "node_output",
        "next_transition": "transition-ConditionalNode-*************"
      }
    ],
    "default_transition": "transition-MergeDataComponent-1752495362620",
    "evaluation_strategy": "all_matches",
    "node_output": {},
    "global_context": {}
  },
  "request_id": "4142c980-e899-4e06-ab9e-e72fe7f52b69",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool:111] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool name: conditional for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool] Tool name: conditional for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - app.components.conditional_component - INFO - [__init__] ConditionalComponent initialized successfully
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool:144] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Processing payload with component conditional for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool] Processing payload with component conditional for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - app.components.conditional_component - INFO - [process] Processing conditional routing for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - app.components.conditional_component - INFO - [_evaluate_all_matches] Condition 1 matched, will route to: transition-ConditionalNode-*************
2025-07-15 10:48:24 - app.components.conditional_component - INFO - [_evaluate_all_matches] Multiple conditions matched (1), routing to: ['transition-ConditionalNode-*************']
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool:148] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Component conditional processed payload successfully for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool] Component conditional processed payload successfully for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool:154] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor returning raw component result for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ComponentSystem - INFO - [_process_message:717] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool conditional executed successfully for RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:24 - ComponentSystem - INFO - [_process_message] Tool conditional executed successfully for RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:24 - ComponentSystem - INFO - [_send_result:1007] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Preparing to send result for component ApiRequestNode, RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer:244] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Creating Kafka producer for component ApiRequestNode with configuration:
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer] Creating Kafka producer for component ApiRequestNode with configuration:
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer:247] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747]   Bootstrap Servers: 34.172.106.233:9092
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer]   Bootstrap Servers: 34.172.106.233:9092
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer:248] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747]   Acks: all (ensuring message is written to all in-sync replicas)
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer]   Acks: all (ensuring message is written to all in-sync replicas)
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer:252] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747]   Request Timeout: 60000ms
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer]   Request Timeout: 60000ms
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer:255] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer:259] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Creating new Kafka producer for component: ApiRequestNode with servers: 34.172.106.233:9092
2025-07-15 10:48:24 - ComponentSystem - INFO - [get_producer] Creating new Kafka producer for component: ApiRequestNode with servers: 34.172.106.233:9092
2025-07-15 10:48:25 - ComponentSystem - INFO - [get_producer:266] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Kafka producer started successfully for component: ApiRequestNode
2025-07-15 10:48:25 - ComponentSystem - INFO - [get_producer] Kafka producer started successfully for component: ApiRequestNode
2025-07-15 10:48:25 - ComponentSystem - INFO - [_send_result:1105] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sending Kafka response: RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69, Response={
  "request_id": "4142c980-e899-4e06-ab9e-e72fe7f52b69",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.942066,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-ConditionalNode-*************"
      ],
      "matched_conditions": [
        1
      ],
      "condition_result": true,
      "execution_time_ms": 5.***************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 1,
      "evaluation_strategy": "all_matches"
    },
    "input_data": {}
  },
  "error": null
}
2025-07-15 10:48:25 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69, Response={
  "request_id": "4142c980-e899-4e06-ab9e-e72fe7f52b69",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.942066,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-ConditionalNode-*************"
      ],
      "matched_conditions": [
        1
      ],
      "condition_result": true,
      "execution_time_ms": 5.***************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 1,
      "evaluation_strategy": "all_matches"
    },
    "input_data": {}
  },
  "error": null
}
2025-07-15 10:48:26 - ComponentSystem - INFO - [_send_result:1121] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sent result for component ApiRequestNode to topic node_results for RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:26 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=4142c980-e899-4e06-ab9e-e72fe7f52b69
2025-07-15 10:48:26 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Successfully committed offset 1210 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:26 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1210 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:26 - ComponentSystem - INFO - [_process_message:936] [ReqID:4142c980-e899-4e06-ab9e-e72fe7f52b69] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1209, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:26 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1209, TaskID=ApiRequestNode-node-execution-request-0-1209-1752556704.052972
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1210, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1210, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hellow",
        "next_transition": "transition-MergeDataComponent-*************"
      }
    ],
    "default_transition": "transition-CombineTextComponent-*************",
    "evaluation_strategy": "all_matches",
    "node_output": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "global_context": {}
  },
  "request_id": "0e561674-624b-459b-855e-a671f4d413bd",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612, Payload={
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hellow",
        "next_transition": "transition-MergeDataComponent-*************"
      }
    ],
    "default_transition": "transition-CombineTextComponent-*************",
    "evaluation_strategy": "all_matches",
    "node_output": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "global_context": {}
  },
  "request_id": "0e561674-624b-459b-855e-a671f4d413bd",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message:713] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool conditional for RequestID=0e561674-624b-459b-855e-a671f4d413bd, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message] Executing tool conditional for RequestID=0e561674-624b-459b-855e-a671f4d413bd, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool:94] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool:97] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hellow",
        "next_transition": "transition-MergeDataComponent-*************"
      }
    ],
    "default_transition": "transition-CombineTextComponent-*************",
    "evaluation_strategy": "all_matches",
    "node_output": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "global_context": {}
  },
  "request_id": "0e561674-624b-459b-855e-a671f4d413bd",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "conditional",
  "tool_parameters": {
    "conditions": [
      {
        "source": "node_output",
        "operator": "equals",
        "expected_value": "hellow",
        "next_transition": "transition-MergeDataComponent-*************"
      }
    ],
    "default_transition": "transition-CombineTextComponent-*************",
    "evaluation_strategy": "all_matches",
    "node_output": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "global_context": {}
  },
  "request_id": "0e561674-624b-459b-855e-a671f4d413bd",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-ConditionalNode-*************",
  "node_label": "Switch-Case Router"
}
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool:111] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool name: conditional for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool] Tool name: conditional for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool:144] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Processing payload with component conditional for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool] Processing payload with component conditional for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - app.components.conditional_component - INFO - [process] Processing conditional routing for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - app.components.conditional_component - INFO - [_evaluate_all_matches] No conditions matched, using default: transition-CombineTextComponent-*************
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool:148] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Component conditional processed payload successfully for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool] Component conditional processed payload successfully for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool:154] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor returning raw component result for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message:717] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool conditional executed successfully for RequestID=0e561674-624b-459b-855e-a671f4d413bd, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message] Tool conditional executed successfully for RequestID=0e561674-624b-459b-855e-a671f4d413bd, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_send_result:1007] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Preparing to send result for component ApiRequestNode, RequestID=0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ComponentSystem - INFO - [_send_result:1105] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sending Kafka response: RequestID=0e561674-624b-459b-855e-a671f4d413bd, Response={
  "request_id": "0e561674-624b-459b-855e-a671f4d413bd",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.0537841,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-CombineTextComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 0.****************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches"
    },
    "input_data": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    }
  },
  "error": null
}
2025-07-15 10:48:29 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=0e561674-624b-459b-855e-a671f4d413bd, Response={
  "request_id": "0e561674-624b-459b-855e-a671f4d413bd",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.0537841,
  "transition_id": "transition-ConditionalNode-*************",
  "result": {
    "routing_decision": {
      "target_transitions": [
        "transition-CombineTextComponent-*************"
      ],
      "matched_conditions": [],
      "condition_result": false,
      "execution_time_ms": 0.****************
    },
    "metadata": {
      "total_conditions": 1,
      "total_matches": 0,
      "evaluation_strategy": "all_matches"
    },
    "input_data": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    }
  },
  "error": null
}
2025-07-15 10:48:29 - ComponentSystem - INFO - [_send_result:1121] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sent result for component ApiRequestNode to topic node_results for RequestID=0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=0e561674-624b-459b-855e-a671f4d413bd
2025-07-15 10:48:29 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Successfully committed offset 1211 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1211 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message:936] [ReqID:0e561674-624b-459b-855e-a671f4d413bd] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1210, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:29 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1210, TaskID=ApiRequestNode-node-execution-request-0-1210-**********.0514612
2025-07-15 10:48:31 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1211, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:31 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1211, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:31 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "num_additional_inputs": 0
  },
  "request_id": "5e466c30-feb9-4ba9-9012-30f1a0f63c80",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:31 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "num_additional_inputs": 0
  },
  "request_id": "5e466c30-feb9-4ba9-9012-30f1a0f63c80",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:31 - ComponentSystem - INFO - [_process_message:713] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool CombineTextComponent for RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:31 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:31 - ToolExecutor - INFO - [execute_tool:94] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:31 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:31 - ToolExecutor - INFO - [execute_tool:97] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "num_additional_inputs": 0
  },
  "request_id": "5e466c30-feb9-4ba9-9012-30f1a0f63c80",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:31 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": {
      "routing_decision": {
        "target_transitions": [
          "transition-ConditionalNode-*************"
        ],
        "matched_conditions": [
          1
        ],
        "condition_result": true,
        "execution_time_ms": 5.***************
      },
      "metadata": {
        "total_conditions": 1,
        "total_matches": 1,
        "evaluation_strategy": "all_matches"
      },
      "input_data": {}
    },
    "num_additional_inputs": 0
  },
  "request_id": "5e466c30-feb9-4ba9-9012-30f1a0f63c80",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool:111] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool name: CombineTextComponent for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [__init__] Initializing Combine Text Component
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [__init__] Combine Text Component initialized successfully
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool:144] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Processing payload with component CombineTextComponent for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'request_id']
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'main_input': {'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}, 'num_additional_inputs': 0, 'request_id': '5e466c30-feb9-4ba9-9012-30f1a0f63c80'}
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'request_id']
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id 5e466c30-feb9-4ba9-9012-30f1a0f63c80. Separator: '', Num additional inputs: 0
2025-07-15 10:48:32 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id 5e466c30-feb9-4ba9-9012-30f1a0f63c80. Result length: 296
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool:148] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Component CombineTextComponent processed payload successfully for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool:154] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor returning raw component result for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: 5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ComponentSystem - INFO - [_process_message:717] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool CombineTextComponent executed successfully for RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:32 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:32 - ComponentSystem - INFO - [_send_result:1007] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Preparing to send result for component ApiRequestNode, RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ComponentSystem - INFO - [_send_result:1105] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sending Kafka response: RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80, Response={
  "request_id": "5e466c30-feb9-4ba9-9012-30f1a0f63c80",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.0027058,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
  "error": null
}
2025-07-15 10:48:32 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80, Response={
  "request_id": "5e466c30-feb9-4ba9-9012-30f1a0f63c80",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.0027058,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
  "error": null
}
2025-07-15 10:48:32 - ComponentSystem - INFO - [_send_result:1121] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sent result for component ApiRequestNode to topic node_results for RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=5e466c30-feb9-4ba9-9012-30f1a0f63c80
2025-07-15 10:48:32 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Successfully committed offset 1212 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:32 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1212 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:32 - ComponentSystem - INFO - [_process_message:936] [ReqID:5e466c30-feb9-4ba9-9012-30f1a0f63c80] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1211, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:32 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1211, TaskID=ApiRequestNode-node-execution-request-0-1211-**********.99843
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1212, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1212, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
    "num_additional_inputs": 0
  },
  "request_id": "bb059495-5775-4d63-93e7-56ba949d5751",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
    "num_additional_inputs": 0
  },
  "request_id": "bb059495-5775-4d63-93e7-56ba949d5751",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message:713] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool CombineTextComponent for RequestID=bb059495-5775-4d63-93e7-56ba949d5751, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message] Executing tool CombineTextComponent for RequestID=bb059495-5775-4d63-93e7-56ba949d5751, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool:94] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Executing tool for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool] Executing tool for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool:97] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
    "num_additional_inputs": 0
  },
  "request_id": "bb059495-5775-4d63-93e7-56ba949d5751",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool] ToolExecutor received payload: {
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
    "num_additional_inputs": 0
  },
  "request_id": "bb059495-5775-4d63-93e7-56ba949d5751",
  "correlation_id": "1d39fcc2-6ebd-4237-9bb5-920d04044747",
  "transition_id": "transition-CombineTextComponent-*************",
  "node_label": "Combine Text"
}
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool:111] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool name: CombineTextComponent for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool] Tool name: CombineTextComponent for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool:144] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Processing payload with component CombineTextComponent for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool] Processing payload with component CombineTextComponent for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - app.components.combine_text_component_new - INFO - [process] Processing text combination request for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - app.components.combine_text_component_new - INFO - [process] PAYLOAD KEYS: ['main_input', 'num_additional_inputs', 'request_id']
2025-07-15 10:48:34 - app.components.combine_text_component_new - INFO - [process] FULL PAYLOAD: {'main_input': "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}", 'num_additional_inputs': 0, 'request_id': 'bb059495-5775-4d63-93e7-56ba949d5751'}
2025-07-15 10:48:34 - app.components.combine_text_component_new - INFO - [process] PARAMETERS KEYS: ['main_input', 'num_additional_inputs', 'request_id']
2025-07-15 10:48:34 - app.components.combine_text_component_new - WARNING - [process] Failed to parse main input as JSON: Expecting property name enclosed in double quotes: line 1 column 2 (char 1)
2025-07-15 10:48:34 - app.components.combine_text_component_new - INFO - [process] Combining text for request_id bb059495-5775-4d63-93e7-56ba949d5751. Separator: '', Num additional inputs: 0
2025-07-15 10:48:34 - app.components.combine_text_component_new - INFO - [process] Text combined successfully for request_id bb059495-5775-4d63-93e7-56ba949d5751. Result length: 296
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool:148] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Component CombineTextComponent processed payload successfully for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool] Component CombineTextComponent processed payload successfully for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool:154] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] ToolExecutor returning raw component result for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ToolExecutor - INFO - [execute_tool] ToolExecutor returning raw component result for request_id: bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message:717] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Tool CombineTextComponent executed successfully for RequestID=bb059495-5775-4d63-93e7-56ba949d5751, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:34 - ComponentSystem - INFO - [_process_message] Tool CombineTextComponent executed successfully for RequestID=bb059495-5775-4d63-93e7-56ba949d5751, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:34 - ComponentSystem - INFO - [_send_result:1007] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Preparing to send result for component ApiRequestNode, RequestID=bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ComponentSystem - INFO - [_send_result] Preparing to send result for component ApiRequestNode, RequestID=bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:34 - ComponentSystem - INFO - [_send_result:1105] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sending Kafka response: RequestID=bb059495-5775-4d63-93e7-56ba949d5751, Response={
  "request_id": "bb059495-5775-4d63-93e7-56ba949d5751",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.969918,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
  "error": null
}
2025-07-15 10:48:34 - ComponentSystem - INFO - [_send_result] Sending Kafka response: RequestID=bb059495-5775-4d63-93e7-56ba949d5751, Response={
  "request_id": "bb059495-5775-4d63-93e7-56ba949d5751",
  "component_type": "ApiRequestNode",
  "status": "success",
  "message": "Request processed successfully",
  "timestamp": **********.969918,
  "transition_id": "transition-CombineTextComponent-*************",
  "result": "{'routing_decision': {'target_transitions': ['transition-ConditionalNode-*************'], 'matched_conditions': [1], 'condition_result': True, 'execution_time_ms': 5.***************}, 'metadata': {'total_conditions': 1, 'total_matches': 1, 'evaluation_strategy': 'all_matches'}, 'input_data': {}}",
  "error": null
}
2025-07-15 10:48:35 - ComponentSystem - INFO - [_send_result:1121] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Sent result for component ApiRequestNode to topic node_results for RequestID=bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:35 - ComponentSystem - INFO - [_send_result] Sent result for component ApiRequestNode to topic node_results for RequestID=bb059495-5775-4d63-93e7-56ba949d5751
2025-07-15 10:48:35 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Successfully committed offset 1213 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:35 - ComponentSystem - INFO - [_commit_offset] Successfully committed offset 1213 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:35 - ComponentSystem - INFO - [_process_message:936] [ReqID:bb059495-5775-4d63-93e7-56ba949d5751] [CorrID:1d39fcc2-6ebd-4237-9bb5-920d04044747] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1212, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
2025-07-15 10:48:35 - ComponentSystem - INFO - [_process_message] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=1212, TaskID=ApiRequestNode-node-execution-request-0-1212-**********.967477
