import time
from typing import List, Dict, Any, ClassVar

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    StringInput,
    BoolInput,
    IntInput,
    HandleInput,
    InputVisibilityRule,
    InputRequirementRule,
    Output,
)
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeR<PERSON>ult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input
from app.constants.semantic_types import ROUTING_DATA


class ConditionalNode(BaseNode):
    """
    Switch-Case Router: Dual-mode conditional routing component.

    **IMPORTANT ARCHITECTURAL NOTE:**
    This component does NOT execute as a separate workflow node. Instead, it generates
    conditional routing logic that is embedded into the previous node's transition in the
    transition schema. The orchestration engine handles all conditional evaluation and routing.

    **DUAL-MODE DESIGN:**
    - **Node Output Mode** (default): All conditions evaluate against the connected input_data
    - **Global Context Mode**: All conditions evaluate against a global variable while input_data flows through for routing
    - Switch between modes using "Data Source" dropdown input
    - Single input_data that gets routed to matching condition outputs

    **Mode Details:**
    - **Direct Mode** (use_variable_for_conditions = false):
      - Conditions evaluate against input_data directly
      - Same data is used for evaluation and routing
      - Simple, straightforward conditional logic
      
    - **Variable Mode** (use_variable_for_conditions = true):
      - Conditions evaluate against a global variable specified in condition_variable_name
      - input_data flows through unchanged for routing
      - Allows checking external variables while routing main data

    This component supports up to 10 conditions with 9 different operators:
    equals, not_equals, contains, starts_with, ends_with, greater_than, less_than, exists, is_empty

    **Frontend Requirements:**
    - Monitor the 'num_additional_conditions' input value changes (0-9)
    - Monitor the 'source' dropdown for UI mode switching
    - Show/hide 'variable_name' field based on source selection (global_context)
    - Dynamically render output handles: condition_1, condition_2, ..., condition_N, default
    - Update handle connections when num_additional_conditions changes
    - Store connections from these dynamic handles with sourceHandle="condition_{i}" or "default"
    - Remove/hide output handles when num_additional_conditions decreases

    **Workflow Schema Generation:**
    - This node does NOT appear in the transition schema's 'nodes' array
    - Instead, it generates 'conditional_routing' logic embedded in the previous node's transition
    - The orchestration engine processes conditional routing directly without separate node execution
    """

    name = "ConditionalNode"
    display_name = "Switch-Case Router"
    description = "Evaluates multiple conditions and routes data to matching outputs"
    category = "Logic"
    icon = "GitBranch"
    beta = False

    # Supported operators for switch-case conditions
    OPERATOR_OPTIONS = [
        "equals", "not_equals", "contains", "starts_with", "ends_with",
        "greater_than", "less_than", "exists", "is_empty"
    ]

    # Source options for condition evaluation - simplified to only node_output
    # Removed "global_context" to simplify the component and eliminate visibility complexity
    SOURCE_OPTIONS = ["node_output"]

    inputs: ClassVar[List[InputBase]] = [
        # Single input that will be routed when conditions match
        create_dual_purpose_input(
            name="input_data",
            display_name="Input Data",
            input_type="multiline",
            required=True,
            info="Input data that will be routed when conditions match. Can be connected from another node or entered directly.",
            input_types=["Any"],
        ),

        # Source selection dropdown
        DropdownInput(
            name="source",
            display_name="Data Source",
            options=["node_output", "global_context"],
            value="node_output",
            required=True,
            info="Select the data source for condition evaluation: 'node_output' uses connected input data, 'global_context' uses a global variable.",
        ),

        # Global variable name (only shown when global_context is selected)
        StringInput(
            name="variable_name",
            display_name="Variable Name",
            value="",
            required=False,  # Changed to False since requirement is conditional
            info="Name of the global variable to evaluate conditions against. This variable should be available in the workflow context.",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="source",
                    field_value="global_context",
                    operator="equals"
                )
            ],
            requirement_rules=[
                InputRequirementRule(
                    field_name="source",
                    field_value="global_context",
                    operator="equals"
                )
            ],
        ),

        # Condition 1 configuration
        DropdownInput(
            name="condition_1_operator",
            display_name="Condition 1 - Operator",
            options=OPERATOR_OPTIONS,
            value="equals",
            required=False,
            info="Comparison operator to apply",
        ),
        StringInput(
            name="condition_1_expected_value",
            display_name="Condition 1 - Expected Value",
            value="",
            required=False,
            info="Value to compare against (not used for exists/is_empty operators)",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="condition_1_operator",
                    field_value="exists",
                    operator="not_equals"
                ),
                InputVisibilityRule(
                    field_name="condition_1_operator",
                    field_value="is_empty",
                    operator="not_equals"
                )
            ],
            visibility_logic="AND",
        ),

        # Dynamic condition management
        IntInput(
            name="num_additional_conditions",
            display_name="Number of Additional Conditions",
            value=0,
            min_value=0,
            max_value=9,
            info="Number of additional conditions beyond the base 1 condition (0-9).",
        ),

        # Evaluation strategy for multiple condition handling
        DropdownInput(
            name="evaluation_strategy",
            display_name="Evaluation Strategy",
            options=[
                "first_match",
                "all_matches"
            ],
            value="all_matches",
            required=False,
            info="Strategy for handling multiple conditions: 'first_match' stops at the first true condition, 'all_matches' executes all true conditions in parallel.",
        ),
    ]

    # Add dynamic condition configuration inputs for conditions 2-10
    # All conditions will evaluate against the single input_data
    MAX_ADDITIONAL_CONDITIONS = 9
    BASE_CONDITIONS = 1

    for i in range(2, BASE_CONDITIONS + MAX_ADDITIONAL_CONDITIONS + 1):  # 2 to 10
        min_additional_needed = i - BASE_CONDITIONS
        visibility_rules = [
            InputVisibilityRule(
                field_name="num_additional_conditions",
                field_value=min_additional_needed - 1,
                operator="greater_than"
            )
        ]

        # Operator for condition i
        inputs.append(DropdownInput(
            name=f"condition_{i}_operator",
            display_name=f"Condition {i} - Operator",
            options=OPERATOR_OPTIONS,
            value="equals",
            required=False,
            info="Comparison operator to apply to the input data",
            visibility_rules=visibility_rules,
        ))

        # Expected value for condition i
        expected_value_visibility_rules = visibility_rules + [
            InputVisibilityRule(
                field_name=f"condition_{i}_operator",
                field_value="exists",
                operator="not_equals"
            ),
            InputVisibilityRule(
                field_name=f"condition_{i}_operator",
                field_value="is_empty",
                operator="not_equals"
            )
        ]
        inputs.append(StringInput(
            name=f"condition_{i}_expected_value",
            display_name=f"Condition {i} - Expected Value",
            value="",
            required=False,
            info="Value to compare against the input data (not used for exists/is_empty operators)",
            visibility_rules=expected_value_visibility_rules,
            visibility_logic="AND",
        ))

    # Define maximum conditions for dynamic output generation
    MAX_CONDITIONS = 10

    # Static outputs - only default is defined here
    # Dynamic outputs (condition_1, condition_2, ..., condition_N)
    # are handled by the frontend based on num_additional_conditions value
    # UPDATED: Single handle per condition (not true/false pairs)
    outputs: ClassVar[List[Output]] = [
        Output(
            name="default",
            display_name="Default",
            output_type="Any",
            semantic_type=ROUTING_DATA,
            info="Outputs data when no conditions match",
        ),
        # NOTE: condition_1, condition_2, ..., condition_N are NOT defined here statically.
        # They are expected to be handled dynamically by the frontend based on 'num_additional_conditions' input.
        # Frontend should render: condition_1, condition_2, ..., condition_N
        # UPDATED: where N = 1 + value of 'num_additional_conditions' input (1-10 total conditions)
        # Each handle routes data ONLY when that specific condition is TRUE
    ]

    def get_dynamic_output_keys(self, num_additional_conditions: int) -> List[str]:
        """
        Get all dynamic output keys for the given number of additional conditions.
        UPDATED: Single handle per condition (not true/false pairs)

        Args:
            num_additional_conditions: Number of additional conditions beyond base 1 (0-9)

        Returns:
            List of output keys including condition outputs and default
        """
        num_additional_conditions = max(0, min(self.MAX_ADDITIONAL_CONDITIONS, int(num_additional_conditions)))
        total_conditions = self.BASE_CONDITIONS + num_additional_conditions
        return [f"condition_{i}" for i in range(1, total_conditions + 1)] + ["default"]

    def get_dynamic_outputs(self, config: Dict[str, Any]) -> List[Output]:
        """
        Get dynamic output definitions based on configuration.
        This method is called by the frontend to generate dynamic output handles.
        UPDATED: Single handle per condition (not true/false pairs)

        Args:
            config: Component configuration containing num_additional_conditions

        Returns:
            List of dynamic Output objects for the frontend
        """
        num_additional_conditions = config.get("num_additional_conditions", 0)
        num_additional_conditions = max(0, min(self.MAX_ADDITIONAL_CONDITIONS, int(num_additional_conditions)))
        total_conditions = self.BASE_CONDITIONS + num_additional_conditions

        dynamic_outputs = []

        # Add condition outputs - single handle per condition
        # ✅ FIXED: Include condition_1 by starting from 1 instead of 2
        for i in range(1, total_conditions + 1):
            dynamic_outputs.append(Output(
                name=f"condition_{i}",
                display_name=f"Condition {i}",
                output_type="Any",
                semantic_type=ROUTING_DATA,
                info=f"Routes data ONLY when condition {i} is TRUE",
            ))

        # Add default output
        dynamic_outputs.append(Output(
            name="default",
            display_name="Default",
            output_type="Any",
            semantic_type=ROUTING_DATA,
            info="Routes data when NO conditions are TRUE",
        ))

        return dynamic_outputs

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get input value from context, prioritizing handle inputs over direct inputs.

        Args:
            input_name: Name of the input to retrieve
            context: The workflow execution context
            default: Default value if input is not found

        Returns:
            The input value or default if not found
        """
        try:
            node_id = context.current_node_id
            if not node_id:
                context.log(f"Warning: No current node ID found for input {input_name}")
                return default

            # Check if there's a value in the node outputs
            node_outputs = context.node_outputs.get(node_id, {})
            if input_name in node_outputs:
                value = node_outputs[input_name]
                context.log(f"Retrieved input {input_name}: {type(value).__name__}")
                return value

            # If not found, return the default
            context.log(f"Input {input_name} not found, using default: {default}")
            return default
        except Exception as e:
            context.log(f"Error retrieving input {input_name}: {e}")
            return default

    def validate_condition_config_direct(self, condition_config: Dict[str, Any], condition_num: int, context: WorkflowContext) -> bool:
        """
        Validate condition configuration for completeness and correctness using direct data approach.

        Args:
            condition_config: Dictionary containing condition configuration with actual_data
            condition_num: The condition number being validated
            context: The workflow execution context

        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            actual_data = condition_config.get("actual_data", "")
            operator = condition_config.get("operator", "")
            expected_value = condition_config.get("expected_value", "")

            # Validate operator
            if operator not in self.OPERATOR_OPTIONS:
                context.log(f"Error: Condition {condition_num} has invalid operator: {operator}")
                return False

            # Validate expected value for operators that require it
            if operator not in ["exists", "is_empty"] and expected_value == "":
                context.log(f"Warning: Condition {condition_num} with operator '{operator}' has empty expected value")
                # Don't fail validation, just warn - empty string might be intentional

            # Note: actual_data can be empty/None - that's valid for some operators like "is_empty"
            context.log(f"Condition {condition_num} validation passed")
            return True

        except Exception as e:
            context.log(f"Error validating condition {condition_num}: {e}")
            return False

    def _evaluate_condition_direct(self, condition_config: Dict[str, Any]) -> bool:
        """
        Evaluate a single condition using direct input data approach.

        Args:
            condition_config: Dictionary containing actual_data, operator, and expected_value

        Returns:
            True if condition matches, False otherwise
        """
        try:
            operator = condition_config.get("operator", "equals")
            expected_value = condition_config.get("expected_value", "")
            actual_value = condition_config.get("actual_data", "")

            # Evaluate based on operator using the actual input data
            return self._apply_operator(operator, actual_value, expected_value)

        except Exception as e:
            # Log error but don't fail the entire execution - skip this condition
            print(f"Warning: Error evaluating condition: {e}")
            return False

    def _apply_operator(self, operator: str, actual_value: Any, expected_value: Any) -> bool:
        """
        Apply the specified operator to compare actual and expected values.

        Args:
            operator: The comparison operator
            actual_value: The actual value to compare
            expected_value: The expected value to compare against

        Returns:
            True if the comparison matches, False otherwise
        """
        try:
            if operator == "equals":
                return str(actual_value) == str(expected_value)

            elif operator == "not_equals":
                return str(actual_value) != str(expected_value)

            elif operator == "contains":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return expected_str in actual_str

            elif operator == "starts_with":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return actual_str.startswith(expected_str)

            elif operator == "ends_with":
                actual_str = str(actual_value) if actual_value is not None else ""
                expected_str = str(expected_value) if expected_value is not None else ""
                return actual_str.endswith(expected_str)

            elif operator == "greater_than":
                try:
                    actual_num = float(actual_value)
                    expected_num = float(expected_value)
                    return actual_num > expected_num
                except (ValueError, TypeError):
                    return False

            elif operator == "less_than":
                try:
                    actual_num = float(actual_value)
                    expected_num = float(expected_value)
                    return actual_num < expected_num
                except (ValueError, TypeError):
                    return False

            elif operator == "exists":
                return actual_value is not None

            elif operator == "is_empty":
                if actual_value is None:
                    return True
                if isinstance(actual_value, str) and actual_value == "":
                    return True
                return False

            else:
                # Invalid operator - skip condition
                print(f"Warning: Unknown operator '{operator}' - skipping condition")
                return False

        except Exception as e:
            print(f"Warning: Error applying operator '{operator}': {e}")
            return False

    # UPDATED: Removed global context resolution since we now use direct input data

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Modern execute method for switch-case conditional logic.

        Args:
            context: The workflow execution context

        Returns:
            NodeResult with success/error status and outputs
        """
        start_time = time.time()
        context.log(f"Executing {self.display_name}...")

        try:
            # Get number of additional conditions to evaluate
            num_additional_conditions = self.get_input_value("num_additional_conditions", context, 0)
            num_additional_conditions = max(0, min(self.MAX_ADDITIONAL_CONDITIONS, int(num_additional_conditions)))  # Clamp to 0-8
            total_conditions = self.BASE_CONDITIONS + num_additional_conditions

            # Initialize all possible dynamic outputs (following SwitchNodeDynamic pattern)
            output_keys = self.get_dynamic_output_keys(num_additional_conditions)
            outputs = {key: None for key in output_keys}  # Initialize all to None
            matched_conditions = []

            context.log(f"Evaluating {total_conditions} conditions (1 base + {num_additional_conditions} additional) with dynamic outputs: {output_keys[:-1]} + default")

            # Get the input data that will be routed (always the same)
            input_data = self.get_input_value("input_data", context, "")
            
            # Determine evaluation mode and get evaluation data
            use_variable_for_conditions = self.get_input_value("use_variable_for_conditions", context, False)
            
            if use_variable_for_conditions:
                # Variable mode: conditions evaluate against a global variable
                condition_variable_name = self.get_input_value("condition_variable_name", context, "")
                if not condition_variable_name:
                    error_msg = "Variable mode enabled but no condition variable name specified"
                    context.log(error_msg)
                    return NodeResult.error(error_msg, time.time() - start_time)
                
                # Get the variable value from global context
                evaluation_data = context.global_context.get(condition_variable_name)
                if evaluation_data is None:
                    context.log(f"Warning: Variable '{condition_variable_name}' not found in global context, using empty string")
                    evaluation_data = ""
                
                context.log(f"Variable mode: evaluating conditions against variable '{condition_variable_name}', routing input_data")
            else:
                # Direct mode: conditions evaluate against input_data directly
                evaluation_data = input_data
                context.log(f"Direct mode: evaluating conditions against input_data directly")

            # Evaluate each condition against the evaluation data
            for condition_num in range(1, total_conditions + 1):
                try:
                    condition_config = {
                        "actual_data": evaluation_data,
                        "operator": self.get_input_value(f"condition_{condition_num}_operator", context, "equals"),
                        "expected_value": self.get_input_value(f"condition_{condition_num}_expected_value", context, ""),
                    }

                    # Validate condition configuration before evaluation
                    if not self.validate_condition_config_direct(condition_config, condition_num, context):
                        context.log(f"Skipping invalid condition {condition_num}")
                        continue

                    # Evaluate the condition using the evaluation data
                    condition_matches = self._evaluate_condition_direct(condition_config)

                    if condition_matches:
                        matched_conditions.append(condition_num)
                        output_name = f"condition_{condition_num}"

                        # Route the input_data (not evaluation_data) to the matched condition output
                        if output_name in outputs:
                            outputs[output_name] = input_data
                            context.log(f"Condition {condition_num} matched - routing input_data to {output_name}")
                        else:
                            context.log(f"Warning: Condition {condition_num} matched but output {output_name} not in dynamic outputs")

                except Exception as e:
                    context.log(f"Warning: Error evaluating condition {condition_num}: {e}")
                    # Continue with other conditions

            # If no conditions matched, route to default output
            if not matched_conditions:
                if "default" in outputs:
                    outputs["default"] = input_data
                    context.log("No conditions matched - routing input_data to default")
                else:
                    context.log("Warning: No conditions matched but default not in dynamic outputs")

            execution_time = time.time() - start_time
            context.log(f"Switch-case evaluation completed. Matched conditions: {matched_conditions}. Time: {execution_time:.2f}s")

            return NodeResult.success(
                outputs=outputs,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Error in switch-case evaluation: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(error_msg, execution_time)
