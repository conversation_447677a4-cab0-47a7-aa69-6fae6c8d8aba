#!/usr/bin/env python3
"""
Test script to validate the single input approach in node executor ConditionalComponent.
This script validates the changes without requiring external dependencies.
"""

import os
import sys
import re


def validate_conditional_component_single_input():
    """Validate that ConditionalComponent has been updated to use single input approach."""
    
    print("🔍 Validating ConditionalComponent single input implementation...")
    
    # Read the ConditionalComponent file
    file_path = "node-executor-service/app/components/conditional_component.py"
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Test 1: Check for single input_data in schema
    if 'input_data: Any = Field(..., description="Single input data that all conditions will evaluate against")' in content:
        print("✅ Found single 'input_data' field in ConditionalRequestSchema")
    else:
        print("❌ 'input_data' field not found in schema")
        return False
    
    # Test 2: Check that old dual-source fields are removed
    if 'global_context: Dict[str, Any]' in content:
        print("❌ Old 'global_context' field still exists - should be removed")
        return False
    else:
        print("✅ Old 'global_context' field successfully removed")
    
    if 'node_output: Any = Field(..., description="Output from previous node")' in content:
        print("❌ Old 'node_output' field still exists - should be removed")
        return False
    else:
        print("✅ Old 'node_output' field successfully removed")
    
    # Test 3: Check ConditionSchema simplification
    if 'source: str = Field(' not in content:
        print("✅ 'source' field removed from ConditionSchema")
    else:
        print("❌ 'source' field still exists in ConditionSchema")
        return False
    
    if 'variable_name: Optional[str]' not in content:
        print("✅ 'variable_name' field removed from ConditionSchema")
    else:
        print("❌ 'variable_name' field still exists in ConditionSchema")
        return False
    
    # Test 4: Check updated documentation
    if "single input data source" in content:
        print("✅ Found single input documentation")
    else:
        print("❌ Single input documentation missing")
        return False
    
    # Test 5: Check condition evaluation method signature
    if 'async def _evaluate_condition(\n        self,\n        condition: Dict[str, Any],\n        input_data: Any\n    ) -> bool:' in content:
        print("✅ Condition evaluation method uses single input_data parameter")
    else:
        print("❌ Condition evaluation method doesn't use single input_data")
        return False
    
    # Test 6: Check that source-based logic is removed
    if 'if source == "node_output":' in content or 'elif source == "global_context":' in content:
        print("❌ Found old source-based evaluation logic - should be removed")
        return False
    else:
        print("✅ Source-based evaluation logic successfully removed")
    
    # Test 7: Check evaluation methods use input_data
    if 'await self._evaluate_condition(condition, input_data)' in content:
        print("✅ Evaluation methods use single input_data")
    else:
        print("❌ Evaluation methods don't use single input_data")
        return False
    
    # Test 8: Check data flow consistency
    if '"input_data": input_data' in content:
        print("✅ Data flow uses consistent input_data")
    else:
        print("❌ Data flow doesn't use consistent input_data")
        return False
    
    print("\n🎉 ConditionalComponent single input validation completed successfully!")
    print("   ✓ Single input_data schema")
    print("   ✓ Removed dual-source complexity")
    print("   ✓ Simplified condition evaluation")
    print("   ✓ Consistent data flow")
    return True


def main():
    """Main validation function."""
    print("=" * 60)
    print("NODE EXECUTOR SINGLE INPUT VALIDATION REPORT")
    print("=" * 60)
    
    # Change to backend directory
    os.chdir("/Users/<USER>/Desktop/ruh_ai/backend")
    
    success = validate_conditional_component_single_input()
    
    if success:
        print(f"\n✅ ALL VALIDATIONS PASSED")
        print("The ConditionalComponent in node executor has been successfully updated to use a single input approach.")
        print("All conditions now evaluate against the same input data source.")
        sys.exit(0)
    else:
        print(f"\n❌ VALIDATION FAILED")
        print("Some issues were found in the single input implementation.")
        sys.exit(1)


if __name__ == "__main__":
    main()