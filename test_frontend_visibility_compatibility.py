#!/usr/bin/env python3
"""
Test script to validate that our ConditionalNode implementation is compatible
with the frontend visibility rule handling system.
"""

import os
import sys
import re
import json


def validate_frontend_visibility_compatibility():
    """Validate that our ConditionalNode follows the frontend visibility patterns."""
    
    print("🔍 Validating frontend visibility compatibility...")
    
    # Read the ConditionalNode file
    file_path = "workflow-service/app/components/control_flow/conditionalNode.py"
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Test 1: Check for proper BoolInput structure
    bool_input_pattern = r'BoolInput\(\s*name="use_variable_for_conditions"'
    if re.search(bool_input_pattern, content):
        print("✅ Found BoolInput for toggle with correct name")
    else:
        print("❌ BoolInput toggle not found or incorrectly named")
        return False
    
    # Test 2: Check for proper StringInput with visibility rules
    string_input_pattern = r'StringInput\(\s*name="condition_variable_name".*?visibility_rules=\[(.*?)\]'
    match = re.search(string_input_pattern, content, re.DOTALL)
    if match:
        print("✅ Found StringInput with visibility rules")
        
        # Test 3: Check for proper InputVisibilityRule structure
        visibility_rule_pattern = r'InputVisibilityRule\(\s*field_name="use_variable_for_conditions"'
        field_value_pattern = r'field_value=True'
        
        if re.search(visibility_rule_pattern, content) and re.search(field_value_pattern, content):
            print("✅ Found proper InputVisibilityRule structure")
        else:
            print("❌ InputVisibilityRule structure incorrect")
            # Debug: Let's see what we actually have
            print(f"   Debug: Looking for field_name pattern...")
            if re.search(visibility_rule_pattern, content):
                print("   ✅ Found field_name pattern")
            else:
                print("   ❌ field_name pattern not found")
            
            if re.search(field_value_pattern, content):
                print("   ✅ Found field_value pattern")
            else:
                print("   ❌ field_value pattern not found")
            
            return False
    else:
        print("❌ StringInput with visibility rules not found")
        return False
    
    # Test 4: Check for proper operator specification
    operator_pattern = r'operator="equals"'
    if re.search(operator_pattern, content):
        print("✅ Found explicit operator specification")
    else:
        print("✅ Using default operator (equals) - this is acceptable")
    
    # Test 5: Check for proper required field setup
    required_pattern = r'name="condition_variable_name".*?required=True'
    if re.search(required_pattern, content, re.DOTALL):
        print("✅ Found required=True for condition variable name")
    else:
        print("❌ condition_variable_name should be required=True")
        return False
    
    return True


def simulate_frontend_visibility_logic():
    """Simulate how the frontend would process our visibility rules."""
    
    print("\n🔍 Simulating frontend visibility processing...")
    
    # This simulates the frontend's evaluateVisibilityRule function
    def evaluate_visibility_rule(rule, config):
        """Simulate frontend visibility rule evaluation."""
        target_value = config.get(rule['field_name'])
        operator = rule.get('operator', 'equals')
        
        if operator == 'equals':
            return target_value == rule['field_value']
        elif operator == 'not_equals':
            return target_value != rule['field_value']
        # Add other operators as needed
        
        return False
    
    # Test cases based on our implementation
    test_cases = [
        {
            'name': 'Toggle OFF - Variable field should be hidden',
            'config': {'use_variable_for_conditions': False},
            'expected': False
        },
        {
            'name': 'Toggle ON - Variable field should be visible',
            'config': {'use_variable_for_conditions': True},
            'expected': True
        },
        {
            'name': 'Toggle undefined - Variable field should be hidden',
            'config': {},
            'expected': False
        },
        {
            'name': 'Toggle string "true" - Variable field should be hidden',
            'config': {'use_variable_for_conditions': 'true'},
            'expected': False
        }
    ]
    
    # Our visibility rule based on our implementation
    visibility_rule = {
        'field_name': 'use_variable_for_conditions',
        'field_value': True,
        'operator': 'equals'
    }
    
    all_passed = True
    
    for test_case in test_cases:
        result = evaluate_visibility_rule(visibility_rule, test_case['config'])
        
        if result == test_case['expected']:
            print(f"✅ {test_case['name']}: PASSED")
        else:
            print(f"❌ {test_case['name']}: FAILED (expected {test_case['expected']}, got {result})")
            all_passed = False
    
    return all_passed


def validate_input_rendering_flow():
    """Validate that our inputs would be processed correctly in the frontend rendering flow."""
    
    print("\n🔍 Validating input rendering flow...")
    
    # Simulate the frontend input processing
    simulated_inputs = [
        {
            'name': 'input_data',
            'display_name': 'Input Data',
            'input_type': 'multiline',
            'required': True,
            'visibility_rules': None  # Always visible
        },
        {
            'name': 'use_variable_for_conditions',
            'display_name': 'Use Variable for Conditions',
            'input_type': 'bool',
            'value': False,
            'visibility_rules': None  # Always visible
        },
        {
            'name': 'condition_variable_name',
            'display_name': 'Condition Variable Name',
            'input_type': 'string',
            'required': True,
            'visibility_rules': [
                {
                    'field_name': 'use_variable_for_conditions',
                    'field_value': True,
                    'operator': 'equals'
                }
            ]
        }
    ]
    
    # Test with different configurations
    test_configs = [
        {'use_variable_for_conditions': False},
        {'use_variable_for_conditions': True}
    ]
    
    all_passed = True
    
    for config in test_configs:
        print(f"\n  📋 Testing with config: {config}")
        
        for input_def in simulated_inputs:
            # Simulate checkInputVisibility logic
            if not input_def['visibility_rules']:
                is_visible = True
            else:
                # Simulate evaluateVisibilityRules with OR logic
                is_visible = any(
                    config.get(rule['field_name']) == rule['field_value']
                    for rule in input_def['visibility_rules']
                )
            
            visibility_status = "VISIBLE" if is_visible else "HIDDEN"
            print(f"    {input_def['name']}: {visibility_status}")
            
            # Validate expected behavior
            if input_def['name'] == 'condition_variable_name':
                expected_visible = config.get('use_variable_for_conditions', False)
                if is_visible != expected_visible:
                    print(f"    ❌ Expected {expected_visible}, got {is_visible}")
                    all_passed = False
                else:
                    print(f"    ✅ Correct visibility")
    
    return all_passed


def check_api_request_pattern_compatibility():
    """Check that our pattern matches the API request body parameter pattern."""
    
    print("\n🔍 Checking API request pattern compatibility...")
    
    # API request body pattern (from analysis)
    api_request_pattern = {
        'controlling_field': 'method',
        'controlled_field': 'body',
        'visibility_values': ['POST', 'PUT', 'PATCH'],
        'logic': 'OR'
    }
    
    # Our conditional pattern
    conditional_pattern = {
        'controlling_field': 'use_variable_for_conditions',
        'controlled_field': 'condition_variable_name',
        'visibility_values': [True],
        'logic': 'OR'
    }
    
    # Check structural compatibility
    compatibility_checks = [
        ('Has controlling field', bool(conditional_pattern['controlling_field'])),
        ('Has controlled field', bool(conditional_pattern['controlled_field'])),
        ('Has visibility values', bool(conditional_pattern['visibility_values'])),
        ('Uses supported logic', conditional_pattern['logic'] in ['AND', 'OR']),
        ('Single boolean value', len(conditional_pattern['visibility_values']) == 1 and isinstance(conditional_pattern['visibility_values'][0], bool))
    ]
    
    all_passed = True
    
    for check_name, passed in compatibility_checks:
        if passed:
            print(f"✅ {check_name}")
        else:
            print(f"❌ {check_name}")
            all_passed = False
    
    return all_passed


def main():
    """Main validation function."""
    print("=" * 70)
    print("FRONTEND VISIBILITY COMPATIBILITY VALIDATION")
    print("=" * 70)
    
    # Change to backend directory
    os.chdir("/Users/<USER>/Desktop/ruh_ai/backend")
    
    # Run all validations
    validations = [
        ("ConditionalNode Structure", validate_frontend_visibility_compatibility),
        ("Frontend Logic Simulation", simulate_frontend_visibility_logic),
        ("Input Rendering Flow", validate_input_rendering_flow),
        ("API Request Pattern Compatibility", check_api_request_pattern_compatibility)
    ]
    
    results = []
    for name, validation_func in validations:
        try:
            result = validation_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ Error in {name}: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("VALIDATION SUMMARY")
    print("=" * 70)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{name}: {status}")
    
    print(f"\nOverall: {passed_count}/{total_count} validations passed")
    
    if passed_count == total_count:
        print("\n🎉 ALL VALIDATIONS PASSED!")
        print("\nOur ConditionalNode implementation is fully compatible with the frontend visibility system:")
        print("  • BoolInput toggle will render as a Switch component")
        print("  • StringInput will show/hide based on toggle state")
        print("  • Uses the same visibility rule evaluation as API request body")
        print("  • Follows established patterns in the codebase")
        print("  • Will work seamlessly with existing frontend infrastructure")
        
        sys.exit(0)
    else:
        print("\n❌ SOME VALIDATIONS FAILED")
        print("Please check the implementation and fix any issues.")
        sys.exit(1)


if __name__ == "__main__":
    main()